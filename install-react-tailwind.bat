@echo off
REM =============================================================================
REM React + Tailwind CSS Complete Setup Script for Windows
REM =============================================================================
REM This script will create a new React project with Vite and configure Tailwind CSS
REM Usage: install-react-tailwind.bat [project-name]
REM Example: install-react-tailwind.bat my-react-app
REM =============================================================================

setlocal enabledelayedexpansion

REM Check if project name is provided
if "%1"=="" (
    echo [ERROR] Please provide a project name
    echo Usage: install-react-tailwind.bat [project-name]
    echo Example: install-react-tailwind.bat my-react-app
    pause
    exit /b 1
)

set PROJECT_NAME=%1

echo [STEP] Starting React + Tailwind CSS setup for project: %PROJECT_NAME%
echo.

REM Step 1: Create React project with Vite
echo [STEP] Creating React project with Vite...
call npm create vite@latest %PROJECT_NAME% -- --template react

if errorlevel 1 (
    echo [ERROR] Failed to create React project
    pause
    exit /b 1
)

echo [SUCCESS] React project created successfully
echo.

REM Step 2: Navigate to project directory
cd %PROJECT_NAME%

REM Step 3: Install dependencies
echo [STEP] Installing React dependencies...
call npm install

if errorlevel 1 (
    echo [ERROR] Failed to install React dependencies
    pause
    exit /b 1
)

echo [SUCCESS] React dependencies installed
echo.

REM Step 4: Install Tailwind CSS and required packages
echo [STEP] Installing Tailwind CSS and PostCSS packages...
call npm install -D tailwindcss@latest postcss@latest autoprefixer@latest @tailwindcss/postcss@latest

if errorlevel 1 (
    echo [ERROR] Failed to install Tailwind CSS packages
    pause
    exit /b 1
)

echo [SUCCESS] Tailwind CSS packages installed
echo.

REM Step 5: Initialize Tailwind CSS configuration
echo [STEP] Initializing Tailwind CSS configuration...
call npx tailwindcss init -p

if errorlevel 1 (
    echo [ERROR] Failed to initialize Tailwind CSS
    pause
    exit /b 1
)

echo [SUCCESS] Tailwind CSS configuration initialized
echo.

REM Step 6: Update tailwind.config.js with proper content paths
echo [STEP] Updating Tailwind configuration...
(
echo export default {
echo   content: [
echo     "./index.html",
echo     "./src/**/*.{js,ts,jsx,tsx}",
echo   ],
echo   theme: {
echo     extend: {},
echo   },
echo   plugins: [],
echo }
) > tailwind.config.js

echo [SUCCESS] Tailwind configuration updated
echo.

REM Step 7: Update PostCSS configuration for Tailwind v4
echo [STEP] Updating PostCSS configuration...
(
echo export default {
echo   plugins: {
echo     '@tailwindcss/postcss': {},
echo     autoprefixer: {},
echo   },
echo }
) > postcss.config.js

echo [SUCCESS] PostCSS configuration updated
echo.

REM Step 8: Update src/index.css with Tailwind directives
echo [STEP] Adding Tailwind directives to CSS...
(
echo @tailwind base;
echo @tailwind components;
echo @tailwind utilities;
) > src/index.css

echo [SUCCESS] Tailwind directives added to CSS
echo.

REM Step 9: Create a sample App.jsx with Tailwind classes
echo [STEP] Creating sample App component with Tailwind classes...
(
echo import React from 'react'
echo.
echo const App = ^(^) =^> {
echo   return ^(
echo     ^<div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"^>
echo       ^<div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4"^>
echo         ^<h1 className="text-3xl font-bold text-center text-gray-800 mb-4"^>
echo           Welcome to React + Tailwind! 🎉
echo         ^</h1^>
echo         ^<p className="text-gray-600 text-center mb-6"^>
echo           Your project is ready with Tailwind CSS configured properly.
echo         ^</p^>
echo         ^<div className="flex justify-center space-x-4"^>
echo           ^<button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300"^>
echo             Primary Button
echo           ^</button^>
echo           ^<button className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-300"^>
echo             Secondary Button
echo           ^</button^>
echo         ^</div^>
echo         ^<div className="mt-6 p-4 bg-green-100 rounded-lg"^>
echo           ^<p className="text-green-800 text-sm text-center"^>
echo             ✅ Tailwind CSS is working perfectly!
echo           ^</p^>
echo         ^</div^>
echo       ^</div^>
echo     ^</div^>
echo   ^)
echo }
echo.
echo export default App
) > src/App.jsx

echo [SUCCESS] Sample App component created
echo.

REM Step 10: Clean up Vite configuration
echo [STEP] Cleaning up Vite configuration...
(
echo import { defineConfig } from 'vite'
echo import react from '@vitejs/plugin-react'
echo.
echo export default defineConfig^({
echo   plugins: [react^(^)],
echo }^)
) > vite.config.js

echo [SUCCESS] Vite configuration cleaned up
echo.

REM Final success message
echo ==========================================
echo [SUCCESS] 🎉 React + Tailwind CSS setup completed!
echo ==========================================
echo.
echo Next steps:
echo 1. cd %PROJECT_NAME%
echo 2. npm run dev
echo.
echo Your project is ready with:
echo ✅ React 19 with Vite
echo ✅ Tailwind CSS v4 with PostCSS
echo ✅ Proper configuration files
echo ✅ Sample component with Tailwind classes
echo.
echo [WARNING] Make sure to test the setup by running 'npm run dev'
echo.
pause
