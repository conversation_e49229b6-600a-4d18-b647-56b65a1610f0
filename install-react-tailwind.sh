#!/bin/bash

# =============================================================================
# React + Tailwind CSS Complete Setup Script
# =============================================================================
# This script will create a new React project with Vite and configure Tailwind CSS
# Usage: ./install-react-tailwind.sh [project-name]
# Example: ./install-react-tailwind.sh my-react-app
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if project name is provided
if [ -z "$1" ]; then
    print_error "Please provide a project name"
    echo "Usage: ./install-react-tailwind.sh [project-name]"
    echo "Example: ./install-react-tailwind.sh my-react-app"
    exit 1
fi

PROJECT_NAME=$1

print_step "Starting React + Tailwind CSS setup for project: $PROJECT_NAME"

# Step 1: Create React project with Vite
print_step "Creating React project with Vite..."
npm create vite@latest $PROJECT_NAME -- --template react

if [ $? -ne 0 ]; then
    print_error "Failed to create React project"
    exit 1
fi

print_success "React project created successfully"

# Step 2: Navigate to project directory
cd $PROJECT_NAME

# Step 3: Install dependencies
print_step "Installing React dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install React dependencies"
    exit 1
fi

print_success "React dependencies installed"

# Step 4: Install Tailwind CSS and required packages
print_step "Installing Tailwind CSS and PostCSS packages..."
npm install -D tailwindcss@latest postcss@latest autoprefixer@latest @tailwindcss/postcss@latest

if [ $? -ne 0 ]; then
    print_error "Failed to install Tailwind CSS packages"
    exit 1
fi

print_success "Tailwind CSS packages installed"

# Step 5: Initialize Tailwind CSS configuration
print_step "Initializing Tailwind CSS configuration..."
npx tailwindcss init -p

if [ $? -ne 0 ]; then
    print_error "Failed to initialize Tailwind CSS"
    exit 1
fi

print_success "Tailwind CSS configuration initialized"

# Step 6: Update tailwind.config.js with proper content paths
print_step "Updating Tailwind configuration..."
cat > tailwind.config.js << 'EOF'
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
EOF

print_success "Tailwind configuration updated"

# Step 7: Update PostCSS configuration for Tailwind v4
print_step "Updating PostCSS configuration..."
cat > postcss.config.js << 'EOF'
export default {
  plugins: {
    '@tailwindcss/postcss': {},
    autoprefixer: {},
  },
}
EOF

print_success "PostCSS configuration updated"

# Step 8: Update src/index.css with Tailwind directives
print_step "Adding Tailwind directives to CSS..."
cat > src/index.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;
EOF

print_success "Tailwind directives added to CSS"

# Step 9: Create a sample App.jsx with Tailwind classes
print_step "Creating sample App component with Tailwind classes..."
cat > src/App.jsx << 'EOF'
import React from 'react'

const App = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
        <h1 className="text-3xl font-bold text-center text-gray-800 mb-4">
          Welcome to React + Tailwind! 🎉
        </h1>
        <p className="text-gray-600 text-center mb-6">
          Your project is ready with Tailwind CSS configured properly.
        </p>
        <div className="flex justify-center space-x-4">
          <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300">
            Primary Button
          </button>
          <button className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-300">
            Secondary Button
          </button>
        </div>
        <div className="mt-6 p-4 bg-green-100 rounded-lg">
          <p className="text-green-800 text-sm text-center">
            ✅ Tailwind CSS is working perfectly!
          </p>
        </div>
      </div>
    </div>
  )
}

export default App
EOF

print_success "Sample App component created"

# Step 10: Clean up Vite configuration (remove unnecessary PostCSS path)
print_step "Cleaning up Vite configuration..."
cat > vite.config.js << 'EOF'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
})
EOF

print_success "Vite configuration cleaned up"

# Final success message
echo ""
echo "=========================================="
print_success "🎉 React + Tailwind CSS setup completed!"
echo "=========================================="
echo ""
echo "Next steps:"
echo "1. cd $PROJECT_NAME"
echo "2. npm run dev"
echo ""
echo "Your project is ready with:"
echo "✅ React 19 with Vite"
echo "✅ Tailwind CSS v4 with PostCSS"
echo "✅ Proper configuration files"
echo "✅ Sample component with Tailwind classes"
echo ""
print_warning "Make sure to test the setup by running 'npm run dev'"
echo ""
